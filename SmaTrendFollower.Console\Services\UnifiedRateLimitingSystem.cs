using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// External API providers with their specific rate limits
/// </summary>
public enum ApiProvider
{
    Alpaca,     // 200 req/min
    Polygon,    // 100 req/sec
    OpenAI,     // ~20 req/min (configurable)
    Gemini,     // 60 req/min (estimated)
    Discord,    // 50 req/sec per bot
    Finnhub     // 30 req/sec + 60 req/min
}

/// <summary>
/// Service priority levels for API rate limiting
/// </summary>
public enum ApiServicePriority
{
    Critical = 1,    // Real-time trading, market data, VIX
    High = 2,        // Signal generation, portfolio updates, order management
    Medium = 3,      // Snapshots, earnings data, notifications
    Low = 4,         // Universe fetching, background tasks, reporting
    Maintenance = 5  // Cache warming, cleanup tasks, analytics
}

/// <summary>
/// Rate limit configuration for each API provider
/// </summary>
public record ApiRateLimitConfig(
    ApiProvider Provider,
    int RequestsPerSecond,
    int RequestsPerMinute,
    int BurstCapacity,
    TimeSpan WindowSize
)
{
    public static readonly Dictionary<ApiProvider, ApiRateLimitConfig> DefaultConfigs = new()
    {
        [ApiProvider.Alpaca] = new(ApiProvider.Alpaca, 4, 200, 10, TimeSpan.FromMinutes(1)),
        [ApiProvider.Polygon] = new(ApiProvider.Polygon, 100, 6000, 50, TimeSpan.FromSeconds(1)),
        [ApiProvider.OpenAI] = new(ApiProvider.OpenAI, 1, 20, 3, TimeSpan.FromMinutes(1)),
        [ApiProvider.Gemini] = new(ApiProvider.Gemini, 1, 60, 5, TimeSpan.FromMinutes(1)),
        [ApiProvider.Discord] = new(ApiProvider.Discord, 50, 3000, 20, TimeSpan.FromSeconds(1)),
        [ApiProvider.Finnhub] = new(ApiProvider.Finnhub, 30, 60, 10, TimeSpan.FromSeconds(1))
    };
}

/// <summary>
/// Request for API rate limiting
/// </summary>
public record ApiRateRequest(
    ApiProvider Provider,
    string ServiceName,
    ApiServicePriority Priority,
    int RequestedCapacity = 1,
    TimeSpan? MaxWait = null
);

/// <summary>
/// Unified rate limiter for all external API services
/// </summary>
public interface IUnifiedRateLimiter
{
    /// <summary>
    /// Request capacity for API calls with priority-based allocation
    /// </summary>
    Task<bool> TryAcquireAsync(ApiRateRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current rate limiting statistics for a provider
    /// </summary>
    ApiRateLimitStats GetStats(ApiProvider provider);

    /// <summary>
    /// Get statistics for all providers
    /// </summary>
    Dictionary<ApiProvider, ApiRateLimitStats> GetAllStats();

    /// <summary>
    /// Update service priority (for dynamic adjustments)
    /// </summary>
    void UpdateServicePriority(string serviceName, ApiServicePriority priority);
}

/// <summary>
/// Unified rate limiter implementation with priority-based allocation for all external APIs
/// </summary>
public sealed class UnifiedRateLimiter : IUnifiedRateLimiter, IDisposable
{
    private readonly ILogger<UnifiedRateLimiter> _logger;
    private readonly ConcurrentDictionary<ApiProvider, ApiProviderLimiter> _providerLimiters;
    private readonly ConcurrentDictionary<string, ApiServicePriority> _servicePriorities;

    public UnifiedRateLimiter(ILogger<UnifiedRateLimiter> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _providerLimiters = new ConcurrentDictionary<ApiProvider, ApiProviderLimiter>();
        _servicePriorities = new ConcurrentDictionary<string, ApiServicePriority>();

        // Initialize limiters for each provider
        foreach (var (provider, config) in ApiRateLimitConfig.DefaultConfigs)
        {
            _providerLimiters[provider] = new ApiProviderLimiter(provider, config, logger);
        }

        _logger.LogInformation("UnifiedRateLimiter initialized for {ProviderCount} API providers", 
            _providerLimiters.Count);
    }

    public async Task<bool> TryAcquireAsync(ApiRateRequest request, CancellationToken cancellationToken = default)
    {
        // Update service priority tracking
        _servicePriorities.AddOrUpdate(request.ServiceName, request.Priority, (_, _) => request.Priority);

        if (!_providerLimiters.TryGetValue(request.Provider, out var limiter))
        {
            _logger.LogError("No rate limiter configured for provider {Provider}", request.Provider);
            return false;
        }

        return await limiter.TryAcquireAsync(request, cancellationToken);
    }

    public ApiRateLimitStats GetStats(ApiProvider provider)
    {
        return _providerLimiters.TryGetValue(provider, out var limiter) 
            ? limiter.GetStats() 
            : new ApiRateLimitStats(provider, 0, 0, 0, DateTime.UtcNow, new(), new());
    }

    public Dictionary<ApiProvider, ApiRateLimitStats> GetAllStats()
    {
        return _providerLimiters.ToDictionary(
            kvp => kvp.Key,
            kvp => kvp.Value.GetStats()
        );
    }

    public void UpdateServicePriority(string serviceName, ApiServicePriority priority)
    {
        _servicePriorities.AddOrUpdate(serviceName, priority, (_, _) => priority);
        _logger.LogInformation("Updated {Service} priority to {Priority}", serviceName, priority);
    }

    public void Dispose()
    {
        foreach (var limiter in _providerLimiters.Values)
        {
            limiter.Dispose();
        }
    }
}

/// <summary>
/// Rate limiter for a specific API provider
/// </summary>
internal sealed class ApiProviderLimiter : IDisposable
{
    private readonly ApiProvider _provider;
    private readonly ApiRateLimitConfig _config;
    private readonly ILogger _logger;
    private readonly Timer _resetTimer;
    private readonly ConcurrentDictionary<ApiServicePriority, int> _priorityUsage;
    
    private volatile int _currentWindowRequests;
    private DateTime _windowStart;
    private readonly object _windowLock = new();

    public ApiProviderLimiter(ApiProvider provider, ApiRateLimitConfig config, ILogger logger)
    {
        _provider = provider;
        _config = config;
        _logger = logger;
        _priorityUsage = new ConcurrentDictionary<ApiServicePriority, int>();
        _windowStart = DateTime.UtcNow;
        
        // Reset window based on provider's window size
        var resetInterval = (int)_config.WindowSize.TotalMilliseconds;
        _resetTimer = new Timer(ResetWindow, null, resetInterval, resetInterval);
    }

    public async Task<bool> TryAcquireAsync(ApiRateRequest request, CancellationToken cancellationToken)
    {
        var maxWait = request.MaxWait ?? TimeSpan.FromSeconds(10);
        var deadline = DateTime.UtcNow.Add(maxWait);
        
        while (DateTime.UtcNow < deadline && !cancellationToken.IsCancellationRequested)
        {
            if (TryAcquireCapacity(request))
            {
                return true;
            }
            
            // Wait based on priority and provider characteristics
            var waitTime = GetWaitTimeForPriority(request.Priority);
            await Task.Delay(waitTime, cancellationToken);
        }
        
        _logger.LogWarning("Rate limit acquisition timeout for {Provider}/{Service} (Priority: {Priority})", 
            _provider, request.ServiceName, request.Priority);
        return false;
    }

    private bool TryAcquireCapacity(ApiRateRequest request)
    {
        lock (_windowLock)
        {
            var availableCapacity = GetAvailableCapacity(request.Priority);
            
            if (_currentWindowRequests + request.RequestedCapacity > availableCapacity)
            {
                return false;
            }
            
            _currentWindowRequests += request.RequestedCapacity;
            _priorityUsage.AddOrUpdate(request.Priority, request.RequestedCapacity, 
                (_, current) => current + request.RequestedCapacity);
        }
        
        _logger.LogDebug("Acquired {Capacity} capacity for {Provider}/{Service} (Priority: {Priority}, Window: {Used}/{Max})",
            request.RequestedCapacity, _provider, request.ServiceName, request.Priority, 
            _currentWindowRequests, GetMaxCapacity());
        
        return true;
    }

    private int GetAvailableCapacity(ApiServicePriority priority)
    {
        var maxCapacity = GetMaxCapacity();
        var higherPriorityUsage = GetUsageForHigherPriorities(priority);
        
        return priority switch
        {
            ApiServicePriority.Critical => maxCapacity,
            ApiServicePriority.High => Math.Max(maxCapacity - higherPriorityUsage, maxCapacity / 4),
            ApiServicePriority.Medium => Math.Max(maxCapacity - higherPriorityUsage, maxCapacity / 8),
            ApiServicePriority.Low => Math.Max(maxCapacity - higherPriorityUsage, maxCapacity / 16),
            ApiServicePriority.Maintenance => Math.Max(maxCapacity - higherPriorityUsage, 1),
            _ => 1
        };
    }

    private int GetMaxCapacity()
    {
        return _config.WindowSize.TotalSeconds <= 1 
            ? _config.RequestsPerSecond 
            : _config.RequestsPerMinute;
    }

    private int GetUsageForHigherPriorities(ApiServicePriority currentPriority)
    {
        return _priorityUsage
            .Where(kvp => kvp.Key < currentPriority)
            .Sum(kvp => kvp.Value);
    }

    private TimeSpan GetWaitTimeForPriority(ApiServicePriority priority)
    {
        var baseWait = _config.WindowSize.TotalMilliseconds / GetMaxCapacity();
        
        return priority switch
        {
            ApiServicePriority.Critical => TimeSpan.FromMilliseconds(baseWait * 0.1),
            ApiServicePriority.High => TimeSpan.FromMilliseconds(baseWait * 0.5),
            ApiServicePriority.Medium => TimeSpan.FromMilliseconds(baseWait * 1.0),
            ApiServicePriority.Low => TimeSpan.FromMilliseconds(baseWait * 2.0),
            ApiServicePriority.Maintenance => TimeSpan.FromMilliseconds(baseWait * 5.0),
            _ => TimeSpan.FromMilliseconds(baseWait)
        };
    }

    private void ResetWindow(object? state)
    {
        lock (_windowLock)
        {
            _currentWindowRequests = 0;
            _windowStart = DateTime.UtcNow;
            _priorityUsage.Clear();
        }
    }

    public ApiRateLimitStats GetStats()
    {
        lock (_windowLock)
        {
            return new ApiRateLimitStats(
                _provider,
                GetMaxCapacity(),
                _currentWindowRequests,
                GetMaxCapacity() - _currentWindowRequests,
                _windowStart,
                new Dictionary<string, ApiServicePriority>(), // Would need to track this separately
                _priorityUsage.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            );
        }
    }

    public void Dispose()
    {
        _resetTimer?.Dispose();
    }
}

/// <summary>
/// Rate limiting statistics for an API provider
/// </summary>
public record ApiRateLimitStats(
    ApiProvider Provider,
    int MaxCapacity,
    int CurrentUsage,
    int AvailableCapacity,
    DateTime WindowStart,
    Dictionary<string, ApiServicePriority> ServicePriorities,
    Dictionary<ApiServicePriority, int> PriorityUsage
);
