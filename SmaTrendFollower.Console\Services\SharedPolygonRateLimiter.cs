using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service priority levels for Polygon API rate limiting
/// </summary>
public enum PolygonServicePriority
{
    Critical = 1,    // Real-time trading, VIX data, market data
    High = 2,        // Signal generation, portfolio updates
    Medium = 3,      // Snapshots, earnings data
    Low = 4,         // Universe fetching, background tasks
    Maintenance = 5  // Cache warming, cleanup tasks
}

/// <summary>
/// Request for Polygon API rate limiting
/// </summary>
public record PolygonRateRequest(
    string ServiceName,
    PolygonServicePriority Priority,
    int RequestedCapacity = 1,
    TimeSpan? MaxWait = null
);

/// <summary>
/// Shared rate limiter for all Polygon API services with priority-based allocation
/// Manages the aggregate 100 req/sec limit across all services
/// </summary>
public interface ISharedPolygonRateLimiter
{
    /// <summary>
    /// Request capacity for API calls with priority-based allocation
    /// </summary>
    Task<bool> TryAcquireAsync(PolygonRateRequest request, CancellationToken cancellationToken = default);

    /// <summary>
    /// Get current rate limiting statistics
    /// </summary>
    PolygonRateLimitStats GetStats();

    /// <summary>
    /// Update service priority (for dynamic adjustments)
    /// </summary>
    void UpdateServicePriority(string serviceName, PolygonServicePriority priority);
}

/// <summary>
/// Shared Polygon rate limiter implementation with priority-based allocation
/// </summary>
public sealed class SharedPolygonRateLimiter : ISharedPolygonRateLimiter, IDisposable
{
    private readonly ILogger<SharedPolygonRateLimiter> _logger;
    private readonly SemaphoreSlim _semaphore;
    private readonly Timer _resetTimer;
    private readonly ConcurrentDictionary<string, PolygonServicePriority> _servicePriorities;
    private readonly ConcurrentDictionary<PolygonServicePriority, int> _priorityUsage;
    
    private const int MaxRequestsPerSecond = 100;
    private const int WindowSizeMs = 1000;
    
    private volatile int _currentWindowRequests;
    private DateTime _windowStart;
    private readonly object _windowLock = new();

    public SharedPolygonRateLimiter(ILogger<SharedPolygonRateLimiter> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _semaphore = new SemaphoreSlim(MaxRequestsPerSecond, MaxRequestsPerSecond);
        _servicePriorities = new ConcurrentDictionary<string, PolygonServicePriority>();
        _priorityUsage = new ConcurrentDictionary<PolygonServicePriority, int>();
        
        _windowStart = DateTime.UtcNow;
        
        // Reset window every second
        _resetTimer = new Timer(ResetWindow, null, WindowSizeMs, WindowSizeMs);
        
        _logger.LogInformation("SharedPolygonRateLimiter initialized with {MaxRps} req/sec limit", MaxRequestsPerSecond);
    }

    public async Task<bool> TryAcquireAsync(PolygonRateRequest request, CancellationToken cancellationToken = default)
    {
        // Update service priority tracking
        _servicePriorities.AddOrUpdate(request.ServiceName, request.Priority, (_, _) => request.Priority);
        
        var maxWait = request.MaxWait ?? TimeSpan.FromSeconds(5);
        var deadline = DateTime.UtcNow.Add(maxWait);
        
        while (DateTime.UtcNow < deadline && !cancellationToken.IsCancellationRequested)
        {
            if (await TryAcquireCapacity(request))
            {
                return true;
            }
            
            // Wait based on priority - higher priority services wait less
            var waitTime = GetWaitTimeForPriority(request.Priority);
            await Task.Delay(waitTime, cancellationToken);
        }
        
        _logger.LogWarning("Rate limit acquisition timeout for {Service} (Priority: {Priority})", 
            request.ServiceName, request.Priority);
        return false;
    }

    private async Task<bool> TryAcquireCapacity(PolygonRateRequest request)
    {
        lock (_windowLock)
        {
            // Check if we have capacity in current window
            if (_currentWindowRequests + request.RequestedCapacity > GetAvailableCapacity(request.Priority))
            {
                return false;
            }
            
            // Reserve the capacity
            _currentWindowRequests += request.RequestedCapacity;
            
            // Track usage by priority
            _priorityUsage.AddOrUpdate(request.Priority, request.RequestedCapacity, 
                (_, current) => current + request.RequestedCapacity);
        }
        
        _logger.LogDebug("Acquired {Capacity} capacity for {Service} (Priority: {Priority}, Window: {Used}/{Max})",
            request.RequestedCapacity, request.ServiceName, request.Priority, _currentWindowRequests, MaxRequestsPerSecond);
        
        return true;
    }

    private int GetAvailableCapacity(PolygonServicePriority priority)
    {
        // Dynamic capacity allocation based on priority
        return priority switch
        {
            PolygonServicePriority.Critical => MaxRequestsPerSecond, // Can use full capacity
            PolygonServicePriority.High => Math.Max(MaxRequestsPerSecond - GetUsageForHigherPriorities(priority), 20),
            PolygonServicePriority.Medium => Math.Max(MaxRequestsPerSecond - GetUsageForHigherPriorities(priority), 10),
            PolygonServicePriority.Low => Math.Max(MaxRequestsPerSecond - GetUsageForHigherPriorities(priority), 5),
            PolygonServicePriority.Maintenance => Math.Max(MaxRequestsPerSecond - GetUsageForHigherPriorities(priority), 2),
            _ => 1
        };
    }

    private int GetUsageForHigherPriorities(PolygonServicePriority currentPriority)
    {
        var usage = 0;
        foreach (var kvp in _priorityUsage)
        {
            if (kvp.Key < currentPriority) // Lower enum value = higher priority
            {
                usage += kvp.Value;
            }
        }
        return usage;
    }

    private TimeSpan GetWaitTimeForPriority(PolygonServicePriority priority)
    {
        return priority switch
        {
            PolygonServicePriority.Critical => TimeSpan.FromMilliseconds(10),
            PolygonServicePriority.High => TimeSpan.FromMilliseconds(50),
            PolygonServicePriority.Medium => TimeSpan.FromMilliseconds(100),
            PolygonServicePriority.Low => TimeSpan.FromMilliseconds(200),
            PolygonServicePriority.Maintenance => TimeSpan.FromMilliseconds(500),
            _ => TimeSpan.FromMilliseconds(100)
        };
    }

    private void ResetWindow(object? state)
    {
        lock (_windowLock)
        {
            _currentWindowRequests = 0;
            _windowStart = DateTime.UtcNow;
            _priorityUsage.Clear();
        }
        
        _logger.LogDebug("Rate limit window reset at {Time}", DateTime.UtcNow);
    }

    public PolygonRateLimitStats GetStats()
    {
        lock (_windowLock)
        {
            return new PolygonRateLimitStats(
                MaxRequestsPerSecond,
                _currentWindowRequests,
                MaxRequestsPerSecond - _currentWindowRequests,
                _windowStart,
                _servicePriorities.ToDictionary(kvp => kvp.Key, kvp => kvp.Value),
                _priorityUsage.ToDictionary(kvp => kvp.Key, kvp => kvp.Value)
            );
        }
    }

    public void UpdateServicePriority(string serviceName, PolygonServicePriority priority)
    {
        _servicePriorities.AddOrUpdate(serviceName, priority, (_, _) => priority);
        _logger.LogInformation("Updated {Service} priority to {Priority}", serviceName, priority);
    }

    public void Dispose()
    {
        _resetTimer?.Dispose();
        _semaphore?.Dispose();
    }
}

/// <summary>
/// Rate limiting statistics
/// </summary>
public record PolygonRateLimitStats(
    int MaxCapacity,
    int CurrentUsage,
    int AvailableCapacity,
    DateTime WindowStart,
    Dictionary<string, PolygonServicePriority> ServicePriorities,
    Dictionary<PolygonServicePriority, int> PriorityUsage
);
