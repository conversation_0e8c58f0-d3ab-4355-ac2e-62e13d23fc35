using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Monitoring service for unified rate limiting system
/// Provides observability, alerting, and automatic adjustments
/// </summary>
public interface IRateLimitMonitoringService
{
    /// <summary>
    /// Get comprehensive rate limit status across all APIs
    /// </summary>
    Task<RateLimitSystemStatus> GetSystemStatusAsync();

    /// <summary>
    /// Get detailed statistics for a specific API provider
    /// </summary>
    Task<ApiProviderStatus> GetProviderStatusAsync(ApiProvider provider);

    /// <summary>
    /// Check if any API is approaching rate limits
    /// </summary>
    Task<List<RateLimitAlert>> CheckForAlertsAsync();

    /// <summary>
    /// Export rate limit metrics for external monitoring
    /// </summary>
    Task<string> ExportMetricsAsync();
}

/// <summary>
/// Background service that monitors rate limiting across all APIs
/// </summary>
public sealed class RateLimitMonitoringService : BackgroundService, IRateLimitMonitoringService
{
    private readonly IUnifiedRateLimiter _unifiedLimiter;
    private readonly ISharedPolygonRateLimiter _polygonLimiter;
    private readonly ILogger<RateLimitMonitoringService> _logger;
    private readonly Timer _alertTimer;
    
    private readonly Dictionary<ApiProvider, RateLimitHistory> _history;
    private readonly object _historyLock = new();

    public RateLimitMonitoringService(
        IUnifiedRateLimiter unifiedLimiter,
        ISharedPolygonRateLimiter polygonLimiter,
        ILogger<RateLimitMonitoringService> logger)
    {
        _unifiedLimiter = unifiedLimiter ?? throw new ArgumentNullException(nameof(unifiedLimiter));
        _polygonLimiter = polygonLimiter ?? throw new ArgumentNullException(nameof(polygonLimiter));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _history = new Dictionary<ApiProvider, RateLimitHistory>();
        foreach (var provider in Enum.GetValues<ApiProvider>())
        {
            _history[provider] = new RateLimitHistory();
        }

        // Check for alerts every 30 seconds
        _alertTimer = new Timer(CheckAlertsCallback, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Rate limit monitoring service started");

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await CollectMetricsAsync();
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in rate limit monitoring");
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
        }

        _logger.LogInformation("Rate limit monitoring service stopped");
    }

    private async Task CollectMetricsAsync()
    {
        var timestamp = DateTime.UtcNow;
        
        // Collect metrics from unified limiter
        var allStats = _unifiedLimiter.GetAllStats();
        
        lock (_historyLock)
        {
            foreach (var (provider, stats) in allStats)
            {
                if (_history.TryGetValue(provider, out var history))
                {
                    history.AddDataPoint(timestamp, stats);
                }
            }
        }

        // Log summary every minute
        if (timestamp.Second == 0)
        {
            await LogSystemSummaryAsync();
        }
    }

    private async Task LogSystemSummaryAsync()
    {
        var status = await GetSystemStatusAsync();
        
        _logger.LogInformation("=== Rate Limit System Summary ===");
        _logger.LogInformation("Total APIs: {ApiCount}, Healthy: {HealthyCount}, Warning: {WarningCount}, Critical: {CriticalCount}",
            status.TotalApis, status.HealthyApis, status.WarningApis, status.CriticalApis);
        
        foreach (var provider in status.ProviderStatuses)
        {
            var utilizationPct = provider.Value.MaxCapacity > 0 
                ? (provider.Value.CurrentUsage * 100.0 / provider.Value.MaxCapacity) 
                : 0;
            
            _logger.LogInformation("{Provider}: {Usage}/{Max} ({Utilization:F1}%) - {Status}",
                provider.Key, provider.Value.CurrentUsage, provider.Value.MaxCapacity, 
                utilizationPct, provider.Value.Status);
        }
    }

    private async void CheckAlertsCallback(object? state)
    {
        try
        {
            var alerts = await CheckForAlertsAsync();
            foreach (var alert in alerts)
            {
                _logger.LogWarning("Rate limit alert: {Provider} - {Message} (Severity: {Severity})",
                    alert.Provider, alert.Message, alert.Severity);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking rate limit alerts");
        }
    }

    public async Task<RateLimitSystemStatus> GetSystemStatusAsync()
    {
        var allStats = _unifiedLimiter.GetAllStats();
        var providerStatuses = new Dictionary<ApiProvider, ApiProviderStatus>();
        
        var healthyCount = 0;
        var warningCount = 0;
        var criticalCount = 0;

        foreach (var (provider, stats) in allStats)
        {
            var utilizationPct = stats.MaxCapacity > 0 
                ? (stats.CurrentUsage * 100.0 / stats.MaxCapacity) 
                : 0;
            
            var status = utilizationPct switch
            {
                < 70 => ApiProviderHealthStatus.Healthy,
                < 90 => ApiProviderHealthStatus.Warning,
                _ => ApiProviderHealthStatus.Critical
            };

            switch (status)
            {
                case ApiProviderHealthStatus.Healthy: healthyCount++; break;
                case ApiProviderHealthStatus.Warning: warningCount++; break;
                case ApiProviderHealthStatus.Critical: criticalCount++; break;
            }

            providerStatuses[provider] = new ApiProviderStatus(
                provider, stats.MaxCapacity, stats.CurrentUsage, stats.AvailableCapacity,
                utilizationPct, status, stats.WindowStart, GetTrendForProvider(provider)
            );
        }

        return new RateLimitSystemStatus(
            allStats.Count, healthyCount, warningCount, criticalCount,
            DateTime.UtcNow, providerStatuses
        );
    }

    public async Task<ApiProviderStatus> GetProviderStatusAsync(ApiProvider provider)
    {
        var stats = _unifiedLimiter.GetStats(provider);
        var utilizationPct = stats.MaxCapacity > 0 
            ? (stats.CurrentUsage * 100.0 / stats.MaxCapacity) 
            : 0;
        
        var status = utilizationPct switch
        {
            < 70 => ApiProviderHealthStatus.Healthy,
            < 90 => ApiProviderHealthStatus.Warning,
            _ => ApiProviderHealthStatus.Critical
        };

        return new ApiProviderStatus(
            provider, stats.MaxCapacity, stats.CurrentUsage, stats.AvailableCapacity,
            utilizationPct, status, stats.WindowStart, GetTrendForProvider(provider)
        );
    }

    public async Task<List<RateLimitAlert>> CheckForAlertsAsync()
    {
        var alerts = new List<RateLimitAlert>();
        var allStats = _unifiedLimiter.GetAllStats();

        foreach (var (provider, stats) in allStats)
        {
            var utilizationPct = stats.MaxCapacity > 0 
                ? (stats.CurrentUsage * 100.0 / stats.MaxCapacity) 
                : 0;

            if (utilizationPct >= 95)
            {
                alerts.Add(new RateLimitAlert(
                    provider, RateLimitAlertSeverity.Critical,
                    $"Rate limit utilization at {utilizationPct:F1}% - immediate action required",
                    DateTime.UtcNow
                ));
            }
            else if (utilizationPct >= 80)
            {
                alerts.Add(new RateLimitAlert(
                    provider, RateLimitAlertSeverity.Warning,
                    $"Rate limit utilization at {utilizationPct:F1}% - monitor closely",
                    DateTime.UtcNow
                ));
            }

            // Check for sustained high usage
            var trend = GetTrendForProvider(provider);
            if (trend == UsageTrend.Increasing && utilizationPct > 60)
            {
                alerts.Add(new RateLimitAlert(
                    provider, RateLimitAlertSeverity.Info,
                    $"Increasing usage trend detected - current: {utilizationPct:F1}%",
                    DateTime.UtcNow
                ));
            }
        }

        return alerts;
    }

    public async Task<string> ExportMetricsAsync()
    {
        var systemStatus = await GetSystemStatusAsync();
        return JsonSerializer.Serialize(systemStatus, new JsonSerializerOptions 
        { 
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
    }

    private UsageTrend GetTrendForProvider(ApiProvider provider)
    {
        lock (_historyLock)
        {
            if (!_history.TryGetValue(provider, out var history))
                return UsageTrend.Stable;

            return history.GetTrend();
        }
    }

    public override void Dispose()
    {
        _alertTimer?.Dispose();
        base.Dispose();
    }
}

/// <summary>
/// Historical data tracking for rate limit usage
/// </summary>
internal class RateLimitHistory
{
    private readonly Queue<(DateTime Timestamp, double UtilizationPct)> _dataPoints = new();
    private const int MaxDataPoints = 60; // Keep 10 minutes of data (10-second intervals)

    public void AddDataPoint(DateTime timestamp, ApiRateLimitStats stats)
    {
        var utilizationPct = stats.MaxCapacity > 0 
            ? (stats.CurrentUsage * 100.0 / stats.MaxCapacity) 
            : 0;

        _dataPoints.Enqueue((timestamp, utilizationPct));
        
        while (_dataPoints.Count > MaxDataPoints)
        {
            _dataPoints.Dequeue();
        }
    }

    public UsageTrend GetTrend()
    {
        if (_dataPoints.Count < 6) return UsageTrend.Stable;

        var recent = _dataPoints.TakeLast(6).Select(dp => dp.UtilizationPct).ToArray();
        var older = _dataPoints.Take(_dataPoints.Count - 6).TakeLast(6).Select(dp => dp.UtilizationPct).ToArray();

        if (older.Length == 0) return UsageTrend.Stable;

        var recentAvg = recent.Average();
        var olderAvg = older.Average();
        var difference = recentAvg - olderAvg;

        return difference switch
        {
            > 10 => UsageTrend.Increasing,
            < -10 => UsageTrend.Decreasing,
            _ => UsageTrend.Stable
        };
    }
}

// Supporting types
public enum ApiProviderHealthStatus { Healthy, Warning, Critical }
public enum RateLimitAlertSeverity { Info, Warning, Critical }
public enum UsageTrend { Increasing, Stable, Decreasing }

public record RateLimitSystemStatus(
    int TotalApis, int HealthyApis, int WarningApis, int CriticalApis,
    DateTime LastUpdated, Dictionary<ApiProvider, ApiProviderStatus> ProviderStatuses
);

public record ApiProviderStatus(
    ApiProvider Provider, int MaxCapacity, int CurrentUsage, int AvailableCapacity,
    double UtilizationPercent, ApiProviderHealthStatus Status, DateTime WindowStart, UsageTrend Trend
);

public record RateLimitAlert(
    ApiProvider Provider, RateLimitAlertSeverity Severity, string Message, DateTime Timestamp
);
